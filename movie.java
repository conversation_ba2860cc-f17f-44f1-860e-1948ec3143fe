import java.util.Scanner;
public class movie {
    public static void main(String[] args) {
        int n=10;
        int totalPrice=0; // Variable to track total price of all tickets
        while(n>0){
            System.out.println("select your ticket type");
            System.out.println("1.platinum");
            System.out.println("2.gold");
            System.out.println("3.silver");
            System.out.println("4.total price");
            Scanner sc=new Scanner(System.in);
            int choice=sc.nextInt();
            int price=0;
            switch(choice){
                case 1:
                price=platinum();
                break;
                case 2:
                price=gold();
                break;
                case 3:
                price=silver();
                break;
                case 4:
                totalPrice=totalPrice+price;
                default:
                System.out.println("invalid choice");
                break;
            }
            System.out.println("price of ticket is "+price);
            n--;
            System.out.println("seats remaining "+n);
        }
        System.out.println("cinema is full");
        System.out.println("Final total price for all tickets: "+totalPrice);
    }
    public static int platinum(){
        return 500;
    }
    public static int gold(){
        return 400;
    }
    public static int silver(){
        return 300;
    }
    publix
}
